name: slush
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  flutter_svg: ^2.0.16
  percent_indicator: ^4.2.4
  smooth_page_indicator: ^1.2.0+3
  responsive_sizer: ^3.3.1
  flutter_holo_date_picker: ^2.0.0
  wheel_slider: ^1.2.2
  dotted_border: ^2.1.0
  fluttertoast: ^8.2.8
  quickalert: ^1.1.0
  image_picker: ^1.1.2
  http: ^1.2.2
  flutter_slidable: ^3.1.1
  expandable_text: ^2.3.0
  animated_toggle_switch: ^0.8.3
  provider: ^6.1.2
  otp_text_field: ^1.1.3
  jiffy:
  geolocator: ^12.0.0
  cached_network_image: ^3.4.1
  shared_preferences: ^2.3.3
  google_sign_in: ^6.2.2
  firebase_core: ^3.8.1
  firebase_messaging: ^15.1.6
  firebase_auth: ^5.3.4
  socket_io_client: ^3.0.2
  device_info_plus: ^11.2.0
  open_mail_app: ^0.4.5
  camera: ^0.11.0+2
  flutter_facebook_auth: ^7.1.1
  permission_handler: ^11.3.1
  image_cropper: ^5.0.0
  video_editor: ^3.0.0
  video_player: ^2.9.2
  http_parser: ^4.0.2
  local_auth: ^2.3.0
  cached_video_player_plus: ^3.0.3
  agora_rtc_engine: ^6.5.0
  geocoding: ^3.0.0
  shimmer: ^3.0.0
  in_app_purchase: ^3.2.0
  emoji_picker_flutter: ^3.1.0
  giphy_get: ^3.5.6
  cloud_firestore: ^5.5.1
  google_mobile_ads: ^5.2.0
  flutter_local_notifications: ^18.0.1
  flutter_shakemywidget: ^1.0.5+1
  sign_in_with_apple: ^6.1.4
  lottie: ^3.2.0
  onesignal_flutter: ^5.2.8
  wakelock_plus: ^1.2.8
  firebase_analytics: ^11.3.6
  firebase_dynamic_links:
  uni_links:

dependency_overrides:
  intl: ^0.18.1

#  chewie:
#  modal_progress_hud_nsn:
#  flutter_screenutil:
#  screenshot:
#  image_gallery_saver:
#  drag_and_drop_lists:
#  uuid:
#  intro_screen_onboarding_flutter: ^1.0.0
#  introduction_screen:
#  intro_slider:
#  syncfusion_flutter_maps:
#  date_time_picker:
#  country_state_city:
#  zego_uikit_prebuilt_call:
#  simple_circular_progress_bar:
#  url_launcher:
#  reels_viewer:
#  getwidget:
#  flash:
#  another_flushbar:
#  top_snackbar_flutter:
#  toastification:
#  dio:
#  flutter_ffmpeg: ^0.4.2
#  flutter_facebook_login:
#  video_trimmer:
#  agora_rtc_engine:
#  permission_handler:
#  intl:
#  connectivity:
#  flutter_ringtone_player:
#  wakelock:


dev_dependencies:
  flutter_test:
    sdk: flutter


  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/bg/
    - assets/icons/
    - assets/icons/bottomnavigationIcon/
    - assets/images/
    - assets/sample/
    - assets/icons/circleavatar/
    - assets/icons/camOff.svg
    - assets/animation/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: baloo
      fonts:
        - asset: assets/fonts/baloo/Baloo-Regular.ttf
    - family: baloo_2
      fonts:
        - asset: assets/fonts/baloo-2/Baloo2-Regular.ttf
    - family: hellix
      fonts:
        - asset: assets/fonts/hellix/HellixTRIAL-Regular.otf
    - family: hellixExtraBold
      fonts:
        - asset: assets/fonts/hellix/HellixTRIAL-ExtraBold.otf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
