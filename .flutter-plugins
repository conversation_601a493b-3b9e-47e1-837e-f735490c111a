# This is a generated file; do not edit or check into version control.
agora_rtc_engine=/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/
cached_video_player_plus=/home/<USER>/.pub-cache/hosted/pub.dev/cached_video_player_plus-3.0.3/
camera=/home/<USER>/.pub-cache/hosted/pub.dev/camera-0.11.1/
camera_android_camerax=/home/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.17/
camera_avfoundation=/home/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19+1/
camera_web=/home/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
cloud_firestore=/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/
cloud_firestore_web=/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/
device_info_plus=/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/
emoji_picker_flutter=/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/
facebook_auth_desktop=/home/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-2.1.1/
file_selector_linux=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_analytics=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/
firebase_analytics_web=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+12/
firebase_auth=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/
firebase_auth_web=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/
firebase_core=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/
firebase_core_web=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/
firebase_dynamic_links=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/
firebase_messaging=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/
firebase_messaging_web=/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/
flutter_facebook_auth=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-7.1.2/
flutter_facebook_auth_web=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-6.1.2/
flutter_local_notifications=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/
flutter_local_notifications_linux=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/
flutter_plugin_android_lifecycle=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
flutter_secure_storage=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
fluttertoast=/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/
geocoding=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding-3.0.0/
geocoding_android=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/
geocoding_ios=/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/
geolocator=/home/<USER>/.pub-cache/hosted/pub.dev/geolocator-12.0.0/
geolocator_android=/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
geolocator_apple=/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
geolocator_web=/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/
geolocator_windows=/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/
google_mobile_ads=/home/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/
google_sign_in=/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0/
google_sign_in_android=/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/
google_sign_in_ios=/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/
google_sign_in_web=/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/
image_cropper=/home/<USER>/.pub-cache/hosted/pub.dev/image_cropper-5.0.1/
image_cropper_for_web=/home/<USER>/.pub-cache/hosted/pub.dev/image_cropper_for_web-3.0.0/
image_picker=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
image_picker_for_web=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
in_app_purchase=/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase-3.2.3/
in_app_purchase_android=/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/
in_app_purchase_storekit=/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.4.0/
iris_method_channel=/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/
local_auth=/home/<USER>/.pub-cache/hosted/pub.dev/local_auth-2.3.0/
local_auth_android=/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/
local_auth_darwin=/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/
local_auth_windows=/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/
onesignal_flutter=/home/<USER>/.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/
open_mail_app=/home/<USER>/.pub-cache/hosted/pub.dev/open_mail_app-0.4.5/
package_info_plus=/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
shared_preferences=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sign_in_with_apple=/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/
sign_in_with_apple_web=/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/
sqflite=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/
uni_links=/home/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/
uni_links_web=/home/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/
url_launcher=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
video_player=/home/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.5/
video_player_android=/home/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.4/
video_player_avfoundation=/home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/
video_player_web=/home/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/
video_thumbnail=/home/<USER>/.pub-cache/hosted/pub.dev/video_thumbnail-0.5.6/
wakelock_plus=/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/
webview_flutter=/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/
webview_flutter_android=/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/
webview_flutter_wkwebview=/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/
