<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="io.agora.infra:aosl:1.2.13.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/d2d35cd59afefb7b526fd843bab17d07/transformed/jetified-aosl-1.2.13.1/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/d2d35cd59afefb7b526fd843bab17d07/transformed/jetified-aosl-1.2.13.1/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/4f30af8c4fcf143665f8184d6b90d0af/transformed/jetified-full-video-av1-codec-dec-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/4f30af8c4fcf143665f8184d6b90d0af/transformed/jetified-full-video-av1-codec-dec-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/34fa56cd988c2a2476a44a1a836d040c/transformed/jetified-full-video-av1-codec-enc-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/34fa56cd988c2a2476a44a1a836d040c/transformed/jetified-full-video-av1-codec-enc-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/f89b4e4c45323245bfe444795f656031/transformed/jetified-full-video-codec-dec-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/f89b4e4c45323245bfe444795f656031/transformed/jetified-full-video-codec-dec-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/0ca1e5d98cb5b64d447798179794acc9/transformed/jetified-full-video-codec-enc-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/0ca1e5d98cb5b64d447798179794acc9/transformed/jetified-full-video-codec-enc-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-voice-drive:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/f7b763f69381e16cfd1eba81d909b922/transformed/jetified-full-voice-drive-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/f7b763f69381e16cfd1eba81d909b922/transformed/jetified-full-voice-drive-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/46f87e7e7aa7d170f3b7c1e966412221/transformed/jetified-full-face-capture-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/46f87e7e7aa7d170f3b7c1e966412221/transformed/jetified-full-face-capture-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-detect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/3e8a54eb2e7a5086359dee3a82eabf64/transformed/jetified-full-face-detect-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/3e8a54eb2e7a5086359dee3a82eabf64/transformed/jetified-full-face-detect-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-vqa:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/15421f3dd59107800a321d835d1092a6/transformed/jetified-full-vqa-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/15421f3dd59107800a321d835d1092a6/transformed/jetified-full-vqa-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/c0423679ce4cc2b6c84a661eadce6a4b/transformed/jetified-aiaec-ll-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/c0423679ce4cc2b6c84a661eadce6a4b/transformed/jetified-aiaec-ll-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/62447409d82331312f11101f238c3d60/transformed/jetified-aiaec-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/62447409d82331312f11101f238c3d60/transformed/jetified-aiaec-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:spatial-audio:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/09b7e09b7b0265d0e48b561778bfba7d/transformed/jetified-spatial-audio-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/09b7e09b7b0265d0e48b561778bfba7d/transformed/jetified-spatial-audio-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-virtual-background:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/f5f0c0d1c5757cfefe40f630c71c87cf/transformed/jetified-full-virtual-background-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/f5f0c0d1c5757cfefe40f630c71c87cf/transformed/jetified-full-virtual-background-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:screen-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/32093253cc48240017fbd87ff24f5a37/transformed/jetified-screen-capture-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/32093253cc48240017fbd87ff24f5a37/transformed/jetified-screen-capture-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-content-inspect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/f9c6a1a40ff1451f0fe8ebf9ea4b1638/transformed/jetified-full-content-inspect-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/f9c6a1a40ff1451f0fe8ebf9ea4b1638/transformed/jetified-full-content-inspect-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:clear-vision:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/13403750a72ef470590eb38b50aef08d/transformed/jetified-clear-vision-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/13403750a72ef470590eb38b50aef08d/transformed/jetified-clear-vision-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:audio-beauty:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/60ac1b4e7f6283919afbb02f8da8b8ba/transformed/jetified-audio-beauty-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/60ac1b4e7f6283919afbb02f8da8b8ba/transformed/jetified-audio-beauty-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/530979cf2c5607675da5783aa26c7009/transformed/jetified-ains-ll-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/530979cf2c5607675da5783aa26c7009/transformed/jetified-ains-ll-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/92cf1c9b9b2fc3e46b210f843462e9cb/transformed/jetified-ains-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/92cf1c9b9b2fc3e46b210f843462e9cb/transformed/jetified-ains-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-rtc-basic:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/bfeee9f50580bc177522725497272400/transformed/jetified-full-rtc-basic-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/bfeee9f50580bc177522725497272400/transformed/jetified-full-rtc-basic-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-sdk:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/71f40c80fd05fdff5516dc36d0cebb5e/transformed/jetified-full-sdk-4.5.2/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/71f40c80fd05fdff5516dc36d0cebb5e/transformed/jetified-full-sdk-4.5.2/assets/PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:iris-rtc:4.5.2-build.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/.gradle/caches/transforms-3/b3efcccecbb887474aabaeefa4cc4928/transformed/jetified-iris-rtc-4.5.2-build.1/assets"><file name="PLACEHOLDER" path="/home/<USER>/.gradle/caches/transforms-3/b3efcccecbb887474aabaeefa4cc4928/transformed/jetified-iris-rtc-4.5.2-build.1/assets/PLACEHOLDER"/></source></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/webview_flutter_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":video_thumbnail" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/video_thumbnail/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/video_player_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/url_launcher_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":uni_links" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/uni_links/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/sqflite_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":sign_in_with_apple" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/sign_in_with_apple/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/shared_preferences_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/permission_handler_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/path_provider_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/package_info_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/wakelock_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":open_mail_app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/open_mail_app/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":onesignal_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/onesignal_flutter/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":iris_method_channel" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/iris_method_channel/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":in_app_purchase_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/in_app_purchase_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":image_cropper" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/image_cropper/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":google_sign_in_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/google_sign_in_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":google_mobile_ads" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/google_mobile_ads/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/geolocator_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/geocoding_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/fluttertoast/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/flutter_secure_storage/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/flutter_plugin_android_lifecycle/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/local_auth_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/image_picker_android/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/flutter_local_notifications/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":flutter_facebook_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/flutter_facebook_auth/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/firebase_core/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/firebase_messaging/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":firebase_dynamic_links" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/firebase_dynamic_links/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":firebase_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/firebase_auth/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":firebase_analytics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/firebase_analytics/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":emoji_picker_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/emoji_picker_flutter/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/device_info_plus/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":cloud_firestore" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/cloud_firestore/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":camera_android_camerax" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/camera_android_camerax/intermediates/library_assets/debug/out"/></dataSet><dataSet config=":agora_rtc_engine" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/agora_rtc_engine/intermediates/library_assets/debug/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/android/app/src/main/assets"/><source path="/home/<USER>/Desktop/Fiverr/Slush_App/build/app/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/home/<USER>/Desktop/Fiverr/Slush_App/android/app/src/debug/assets"/></dataSet></merger>