-- Merging decision tree log ---
manifest
ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:1:1-11:12
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:1:1-11:12
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:1:1-11:12
	package
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:2:3-53
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:4:3-65
	android:name
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:4:20-62
application
ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:5:3-10:17
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:6:5-9:15
	android:name
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:6:14-85
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.dynamiclinks.FlutterFirebaseAppRegistrar
ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:7:7-8:86
	android:value
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:8:18-83
	android:name
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:7:18-132
uses-sdk
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
		ADDED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
		INJECTED from /home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
