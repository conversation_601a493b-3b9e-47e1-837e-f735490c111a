1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.flutter.plugins.firebase.dynamiclinks" >
4
5    <uses-sdk
6        android:minSdkVersion="21"
6-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
7        android:targetSdkVersion="21" />
7-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml
8
9    <uses-permission android:name="android.permission.INTERNET" />
9-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:4:3-65
9-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:4:20-62
10
11    <application>
11-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:5:3-10:17
12        <service android:name="com.google.firebase.components.ComponentDiscoveryService" >
12-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:6:5-9:15
12-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:6:14-85
13            <meta-data
13-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:7:7-8:86
14                android:name="com.google.firebase.components:io.flutter.plugins.firebase.dynamiclinks.FlutterFirebaseAppRegistrar"
14-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:7:18-132
15                android:value="com.google.firebase.components.ComponentRegistrar" />
15-->/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/android/src/main/AndroidManifest.xml:8:18-83
16        </service>
17    </application>
18
19</manifest>
