<component name="libraryTable">
  <library name="Flutter Plugins" type="FlutterPluginsLibraryType">
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_purchase-3.2.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geolocator-12.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cached_video_player_plus-3.0.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker-1.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+12" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/uni_links-0.5.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in-6.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_cropper-5.0.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/facebook_auth_desktop-2.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_mail_app-0.4.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core-3.13.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/camera-0.11.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-6.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_facebook_auth-7.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth-2.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.17" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_cropper_for_web-3.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geocoding-3.0.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.4.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player-2.9.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_thumbnail-0.5.6" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_android-2.8.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/camera_web-0.3.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/video_player_web-2.3.5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sqflite-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler-11.4.0" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>