{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "agora_rtc_engine", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/", "native_build": true, "dependencies": ["iris_method_channel"]}, {"name": "camera_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19+1/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "native_build": true, "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_dynamic_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_facebook_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-7.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": []}, {"name": "geocoding_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_ios-3.0.2/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "google_mobile_ads", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/", "native_build": true, "dependencies": ["webview_flutter_wkwebview"]}, {"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_cropper-5.0.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": []}, {"name": "in_app_purchase_storekit", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "iris_method_channel", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/", "native_build": true, "dependencies": []}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "onesignal_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/", "native_build": true, "dependencies": []}, {"name": "open_mail_app", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/open_mail_app-0.4.5/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "uni_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "video_thumbnail", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_thumbnail-0.5.6/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "agora_rtc_engine", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/", "native_build": true, "dependencies": ["iris_method_channel"]}, {"name": "camera_android_camerax", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_android_camerax-0.6.17/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "native_build": true, "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_dynamic_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_facebook_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-7.1.2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": []}, {"name": "geocoding_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geocoding_android-3.3.1/", "native_build": true, "dependencies": []}, {"name": "geolocator_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/", "native_build": true, "dependencies": []}, {"name": "google_mobile_ads", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/", "native_build": true, "dependencies": ["webview_flutter_android"]}, {"name": "google_sign_in_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/", "native_build": true, "dependencies": []}, {"name": "image_cropper", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_cropper-5.0.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "in_app_purchase_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_android-0.4.0+1/", "native_build": true, "dependencies": []}, {"name": "iris_method_channel", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/", "native_build": true, "dependencies": []}, {"name": "local_auth_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "onesignal_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/onesignal_flutter-5.3.3/", "native_build": true, "dependencies": []}, {"name": "open_mail_app", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/open_mail_app-0.4.5/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": []}, {"name": "sqflite_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/", "native_build": true, "dependencies": []}, {"name": "uni_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.4/", "native_build": true, "dependencies": []}, {"name": "video_thumbnail", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_thumbnail-0.5.6/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/", "native_build": true, "dependencies": []}], "macos": [{"name": "agora_rtc_engine", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/", "native_build": true, "dependencies": ["iris_method_channel"]}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "native_build": true, "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "native_build": true, "dependencies": []}, {"name": "facebook_auth_desktop", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-2.1.1/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_local_notifications", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": []}, {"name": "geolocator_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "google_sign_in_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "in_app_purchase_storekit", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "iris_method_channel", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/", "native_build": true, "dependencies": []}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sign_in_with_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/", "native_build": true, "dependencies": []}, {"name": "sqflite_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"]}, {"name": "webview_flutter_wkwebview", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "native_build": false, "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "native_build": true, "dependencies": []}, {"name": "file_selector_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"]}], "windows": [{"name": "agora_rtc_engine", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/", "native_build": true, "dependencies": ["iris_method_channel"]}, {"name": "cloud_firestore", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "native_build": false, "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "native_build": true, "dependencies": []}, {"name": "file_selector_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": []}, {"name": "geolocator_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/", "native_build": true, "dependencies": []}, {"name": "image_picker_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "iris_method_channel", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/", "native_build": true, "dependencies": []}, {"name": "local_auth_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"]}], "web": [{"name": "agora_rtc_engine", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/agora_rtc_engine-6.5.2/", "dependencies": ["iris_method_channel"]}, {"name": "camera_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": []}, {"name": "cloud_firestore_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/", "dependencies": ["firebase_core_web"]}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.3.0/", "dependencies": []}, {"name": "emoji_picker_flutter", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/emoji_picker_flutter-3.1.0/", "dependencies": []}, {"name": "firebase_analytics_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+12/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_auth_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/", "dependencies": ["firebase_core_web"]}, {"name": "flutter_facebook_auth_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-6.1.2/", "dependencies": []}, {"name": "flutter_secure_storage_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": []}, {"name": "geolocator_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/", "dependencies": []}, {"name": "google_sign_in_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": []}, {"name": "image_cropper_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_cropper_for_web-3.0.0/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": []}, {"name": "iris_method_channel", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/iris_method_channel-2.2.2/", "dependencies": []}, {"name": "package_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": []}, {"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "sign_in_with_apple_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/", "dependencies": []}, {"name": "uni_links_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/", "dependencies": []}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": []}, {"name": "video_player_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/", "dependencies": []}, {"name": "wakelock_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "dependencies": ["package_info_plus"]}]}, "dependencyGraph": [{"name": "agora_rtc_engine", "dependencies": ["iris_method_channel"]}, {"name": "cached_video_player_plus", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "camera", "dependencies": ["camera_android_camerax", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android_camerax", "dependencies": []}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "device_info_plus", "dependencies": []}, {"name": "emoji_picker_flutter", "dependencies": ["shared_preferences"]}, {"name": "facebook_auth_desktop", "dependencies": ["flutter_secure_storage"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_dynamic_links", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_facebook_auth", "dependencies": ["flutter_facebook_auth_web", "facebook_auth_desktop"]}, {"name": "flutter_facebook_auth_web", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "fluttertoast", "dependencies": []}, {"name": "geocoding", "dependencies": ["geocoding_android", "geocoding_ios"]}, {"name": "geocoding_android", "dependencies": []}, {"name": "geocoding_ios", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "google_mobile_ads", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview", "webview_flutter"]}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_cropper", "dependencies": ["image_cropper_for_web"]}, {"name": "image_cropper_for_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "in_app_purchase", "dependencies": ["in_app_purchase_android", "in_app_purchase_storekit"]}, {"name": "in_app_purchase_android", "dependencies": []}, {"name": "in_app_purchase_storekit", "dependencies": []}, {"name": "iris_method_channel", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "onesignal_flutter", "dependencies": []}, {"name": "open_mail_app", "dependencies": ["url_launcher"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sign_in_with_apple", "dependencies": ["sign_in_with_apple_web"]}, {"name": "sign_in_with_apple_web", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "uni_links", "dependencies": ["uni_links_web"]}, {"name": "uni_links_web", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "video_thumbnail", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-06-01 10:43:30.109758", "version": "3.27.3", "swift_package_manager_enabled": false}